#!/usr/bin/env python3
"""
Script to run <PERSON><PERSON> worker for background file processing.

Usage:
    python run_worker.py

This script starts a Huey worker that will process background tasks
for file processing using SQLite as the backend.
"""

import logging
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('worker.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function to start Huey worker."""
    try:
        # Import huey instance from queue service
        from services.queue_service import huey
        
        logger.info("Starting Huey worker for file processing...")
        logger.info(f"Queue database: {huey.storage.filename}")
        logger.info("Worker is ready to process tasks")
        
        # Start the consumer (worker)
        # This will run indefinitely and process tasks from the queue
        from huey.consumer import Consumer
        
        consumer = Consumer(huey)
        consumer.run()
        
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Error starting worker: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
