# Red.ai Knowledge Processing Service

Dịch vụ xử lý tài liệu thành embeddings cho AI/RAG.

## Tính năng

* Xử lý tệp có sẵn từ S3/CDN
* Chuyển đổi tài liệu sang Markdown
* Xử lý OCR cho PDF dạng ảnh sử dụng Tesseract và OpenCV
* Chia nội dung thành các đoạn nhỏ (chunks)
* Tạo embeddings sử dụng Google Gemini và Jina AI
* Lưu trữ các đoạn và embeddings trong PostgreSQL
* Tìm kiếm nội dung tương tự về ngữ nghĩa
* Tự động chuyển đổi giữa các nhà cung cấp embedding

## Cài đặt

### Yêu cầu

1. Python 3.9+
2. PostgreSQL với extension pgvector
3. FFmpeg (cần thiết cho thư viện pydub)
4. Tesseract OCR (cần thiết cho xử lý OCR với PDF dạng ảnh)

### Cài đặt FFmpeg (Bắt buộc)

FFmpeg cần thiết cho việc xử lý âm thanh và chuyển đổi PDF. Nếu không có, bạn sẽ thấy cảnh báo và một số chức năng sẽ bị hạn chế.

#### Windows
1. Tải FFmpeg từ [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
2. Giải nén các tệp vào một thư mục (ví dụ: `C:\ffmpeg`)
3. Thêm thư mục `bin` vào biến môi trường PATH
4. Kiểm tra cài đặt với lệnh `ffmpeg -version` trong command prompt mới

#### macOS
```bash
brew install ffmpeg
ffmpeg -version  # Kiểm tra cài đặt
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
ffmpeg -version  # Kiểm tra cài đặt
```

### Cài đặt Tesseract OCR (Bắt buộc cho OCR)

Tesseract OCR cần thiết cho việc xử lý PDF dạng ảnh. Nếu không có, chức năng OCR sẽ không hoạt động.

#### Windows
1. Tải trình cài đặt Tesseract từ [https://github.com/UB-Mannheim/tesseract/wiki](https://github.com/UB-Mannheim/tesseract/wiki)
2. Cài đặt với dữ liệu ngôn ngữ bổ sung (Tiếng Việt và Tiếng Anh)
3. Thêm Tesseract vào biến môi trường PATH
4. Kiểm tra cài đặt với lệnh `tesseract --version` trong command prompt mới

#### macOS
```bash
brew install tesseract
brew install tesseract-lang  # Cho các ngôn ngữ bổ sung
tesseract --version  # Kiểm tra cài đặt
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install tesseract-ocr
sudo apt install tesseract-ocr-vie tesseract-ocr-eng  # Dữ liệu ngôn ngữ Tiếng Việt và Tiếng Anh
tesseract --version  # Kiểm tra cài đặt
```

### Cài đặt các gói phụ thuộc Python

```bash
pip install -r requirements.txt
```

## Cấu hình

Tạo tệp `.env` trong thư mục gốc với các biến sau:

```
# Cài đặt API key
API_KEY=your-secure-api-key-here

# Cài đặt cơ sở dữ liệu
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=redai
DB_SSL=false

# Cài đặt CloudFlare R2 Storage
CF_R2_ACCESS_KEY=your_access_key
CF_R2_SECRET_KEY=your_secret_key
CF_R2_ENDPOINT=your_endpoint
CF_BUCKET_NAME=your_bucket_name
CF_R2_REGION=auto

# Cài đặt API Embedding
GEMINI_API_KEY=your_gemini_api_key
JINA_API_KEY=your_jina_api_key

# Cài đặt OCR
OCR_ENABLED=true
OCR_IMAGE_MIN_SIZE=100
OCR_DPI=300
OCR_LANGUAGE=vie+eng
OCR_PAGE_SEGMENTATION_MODE=3
OCR_IMAGE_THRESHOLD=true
```

## Chạy ứng dụng

### Chạy API server

```bash
uvicorn main:app --host 0.0.0.0 --port 3000 --reload
```

### Chạy Huey worker (cho xử lý background)

Mở terminal mới và chạy:

```bash
python run_worker.py
```

API sẽ có sẵn tại http://localhost:3000

**Lưu ý:** Để xử lý file trong background, bạn cần chạy cả API server và Huey worker.

## Tài liệu API

Swagger UI: http://localhost:3000/docs
ReDoc: http://localhost:3000/redoc

## Xác thực API

Hệ thống sử dụng xác thực bằng API key. Mọi yêu cầu API (ngoại trừ trang tài liệu) đều yêu cầu API key hợp lệ.

### Cách sử dụng API key

1. **Tạo API key mới**:
   - Gọi endpoint `POST /api/auth/keys` với dữ liệu:
   ```json
   {
     "name": "Tên API key của bạn",
     "expires_at": null
   }
   ```
   - Hệ thống sẽ trả về API key mới. Lưu trữ API key này một cách an toàn vì nó chỉ được hiển thị một lần.
   - Endpoint này không yêu cầu xác thực, bạn có thể gọi nó mà không cần API key.

2. **Sử dụng API key**:
   - Thêm header `X-API-Key` vào mọi yêu cầu API với giá trị là API key của bạn:
   ```
   X-API-Key: redai-550e8400-e29b-41d4-a716-446655440000
   ```

3. **Quản lý API key**:
   - Lấy danh sách API key: `GET /api/auth/keys`
   - Hủy API key: `DELETE /api/auth/keys/{key}`

Lưu ý: Khi khởi động lần đầu, hệ thống sẽ tự động tạo một API key mặc định. Bạn có thể sử dụng API key này hoặc tạo API key mới.

## Sử dụng Docker

### 1. Tạo tệp docker-compose.yml

Tạo tệp `docker-compose.yml` với nội dung sau:

```yaml
version: '3.8'

services:
  app:
    image: sonpl2603/redai-rag-engine:latest
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./temp-s3:/app/temp-s3
      - ./queue_data:/app/queue_data
      - ./app.log:/app/app.log
    restart: unless-stopped
    depends_on:
      - worker

  worker:
    image: sonpl2603/redai-rag-engine:latest
    env_file:
      - .env
    volumes:
      - ./temp-s3:/app/temp-s3
      - ./queue_data:/app/queue_data
      - ./worker.log:/app/worker.log
    command: ["python", "run_worker.py"]
    restart: unless-stopped
```

### 2. Chạy ứng dụng

```bash
# Kéo hình ảnh từ Docker Hub
docker pull sonpl2603/redai-rag-engine:latest

# Chạy ứng dụng
docker-compose up -d
```

### 3. Kiểm tra logs

Nếu ứng dụng không hoạt động, kiểm tra logs:

```bash
docker-compose logs
```
