"""Database connection and session management."""

import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import get_settings

# Configure logger
logger = logging.getLogger(__name__)

# Get database URL from settings
settings = get_settings()
DATABASE_URL = settings.DATABASE_URL

# Create SQLAlchemy engine with appropriate configuration
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Check connection before using
    pool_size=10,  # Connection pool size
    max_overflow=20,  # Allow up to 20 connections to overflow from the pool
    pool_recycle=3600,  # Recycle connections after 1 hour
    echo=False,  # SQL echo for debugging (set to True for development)
)

# Create sessionmaker for creating SQLAlchemy sessions
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create a base class for SQLAlchemy models
Base = declarative_base()

def init_db():
    """Initialize the database by creating all tables."""
    try:
        # Import models here to avoid circular imports
        from models.file import File
        from models.document_chunks import DocumentChunk
        from models.api_key import ApiKey
        from models.vector_store import VectorStore

        # Create database tables with checkfirst=True to avoid errors if tables already exist
        Base.metadata.create_all(bind=engine, checkfirst=True)

        # Create default API key if none exists
        from sqlalchemy.orm import Session
        with Session(engine) as session:
            api_key_count = session.query(ApiKey).count()
            if api_key_count == 0:
                # Create a default API key
                default_key = ApiKey.create_new(name="Default API Key")
                session.add(default_key)
                session.commit()
                logger.info(f"Created default API key: {default_key.key}")

        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

# Dependency function to get a database session
def get_db():
    """Get a database session for use in FastAPI dependency injection."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session():
    """Get a database session for use in background tasks."""
    return SessionLocal()