"""Service for converting various file types to Markdown."""

import logging
import os
import json
import csv
import xml.etree.ElementTree as ET
from pathlib import Path
from markitdown import MarkItDown

# Import the image processing service
from services.image_processing_service import process_pdf_with_ocr, process_image_with_ocr

# Configure logger
logger = logging.getLogger(__name__)

def is_file_valid(file_path: str) -> bool:
    """
    Check if file exists and is readable.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if file is valid, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return False

        if not os.path.isfile(file_path):
            logger.error(f"Path is not a file: {file_path}")
            return False

        if os.path.getsize(file_path) == 0:
            logger.warning(f"File is empty: {file_path}")
            return False

        # Try to read first few bytes to check if file is readable
        with open(file_path, 'rb') as f:
            f.read(10)

        return True

    except Exception as e:
        logger.error(f"File validation failed for {file_path}: {str(e)}")
        return False

def convert_file_to_markdown(file_path: str, use_ocr: bool = True) -> str:
    """
    Convert a file to Markdown format using markitdown library or OCR for PDFs and images.

    Args:
        file_path (str): Path to the file to convert
        use_ocr (bool): Whether to use OCR for PDFs and images

    Returns:
        str: The Markdown content

    Raises:
        Exception: If conversion fails
    """
    try:
        # Validate file first
        if not is_file_valid(file_path):
            raise Exception(f"File validation failed: {file_path}")

        # Get file extension for file type detection
        file_extension = Path(file_path).suffix.lower()
        logger.info(f"Converting file to Markdown: {file_path} (type: {file_extension})")

        # Create MarkItDown converter instance
        converter = MarkItDown()
        markdown_content = ""

        # Convert based on file type
        if file_extension in ['.pdf']:
            markdown_content = convert_pdf_to_markdown(file_path, converter, use_ocr)

        # Handle image files with OCR
        elif file_extension in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', '.gif']:
            markdown_content = convert_image_to_markdown(file_path, use_ocr)

        elif file_extension in ['.docx', '.doc']:
            markdown_content = convert_office_to_markdown(file_path, converter, "Word document")

        elif file_extension in ['.pptx', '.ppt']:
            markdown_content = convert_office_to_markdown(file_path, converter, "PowerPoint presentation")

        elif file_extension in ['.xlsx', '.xls']:
            markdown_content = convert_office_to_markdown(file_path, converter, "Excel spreadsheet")

        elif file_extension in ['.txt', '.md', '.rst']:
            markdown_content = convert_text_to_markdown(file_path, file_extension)

        elif file_extension in ['.html', '.htm']:
            markdown_content = convert_office_to_markdown(file_path, converter, "HTML")

        elif file_extension in ['.csv']:
            markdown_content = convert_csv_to_markdown(file_path)

        elif file_extension in ['.json']:
            markdown_content = convert_json_to_markdown(file_path)

        elif file_extension in ['.xml']:
            markdown_content = convert_xml_to_markdown(file_path)

        elif file_extension in ['.rtf']:
            markdown_content = convert_office_to_markdown(file_path, converter, "RTF document")

        elif file_extension in ['.odt', '.ods', '.odp']:
            markdown_content = convert_office_to_markdown(file_path, converter, "OpenDocument")

        else:
            # Unsupported file type - attempt generic conversion
            markdown_content = convert_generic_to_markdown(file_path, converter, file_extension)

        # Debug: Ghi log để kiểm tra loại và nội dung
        logger.debug(f"Type of markdown_content: {type(markdown_content)}")
        logger.debug(f"Markdown content preview: {markdown_content[:200]}")

        # Clean up the markdown content
        markdown_content = clean_markdown(markdown_content)

        logger.info(f"Successfully converted file to Markdown ({len(markdown_content)} characters)")
        return markdown_content

    except Exception as e:
        logger.error(f"Error converting file to Markdown: {str(e)}")
        raise Exception(f"Failed to convert file to Markdown: {str(e)}")

def convert_pdf_to_markdown(file_path: str, converter: MarkItDown, use_ocr: bool) -> str:
    """Convert PDF to markdown with fallback mechanism."""
    try:
        if use_ocr:
            # Try OCR-based PDF processing first
            logger.info("Converting PDF to Markdown using OCR")
            try:
                markdown_content = process_pdf_with_ocr(
                    pdf_path=file_path,
                    dpi=300,
                    lang="eng+vie",
                    apply_grayscale=True,
                    apply_sharpen=True,
                    apply_threshold=True,
                    threshold_value=150
                )
                if markdown_content and len(markdown_content.strip()) > 0:
                    return format_ocr_text_as_markdown(markdown_content)
                else:
                    logger.warning("OCR produced no content, falling back to markitdown")
            except Exception as ocr_error:
                logger.warning(f"OCR failed: {str(ocr_error)}, falling back to markitdown")

        # Fallback to traditional PDF conversion
        logger.info("Converting PDF to Markdown using markitdown")
        result = converter.convert(file_path)
        content = result.text if hasattr(result, 'text') else str(result)

        if not content or len(content.strip()) < 50:
            logger.warning("MarkItDown produced minimal content, this might be an image-based PDF")
            return f"# PDF Document\n\n*Note: This appears to be an image-based PDF. Content extraction was limited.*\n\n{content}"

        return content

    except Exception as e:
        logger.error(f"All PDF conversion methods failed: {str(e)}")
        return f"# PDF Document\n\n*Error: Could not extract content from this PDF file.*\n\nError details: {str(e)}"

def convert_image_to_markdown(file_path: str, use_ocr: bool) -> str:
    """Convert image to markdown."""
    try:
        if use_ocr:
            # Use OCR for image processing
            logger.info("Processing image with OCR")
            ocr_text = process_image_with_ocr(
                image_path=file_path,
                lang="eng+vie",
                apply_grayscale=True,
                apply_sharpen=True,
                apply_threshold=True,
                threshold_value=150
            )
            # Format the OCR text as markdown
            return format_ocr_text_as_markdown(ocr_text)
        else:
            # For images without OCR, create a simple markdown with image reference
            logger.info("Creating markdown with image reference")
            image_filename = os.path.basename(file_path)
            return f"![{image_filename}]({file_path})\n\n"
    except Exception as e:
        logger.error(f"Error processing image: {str(e)}")
        image_filename = os.path.basename(file_path)
        return f"# Image File\n\n![{image_filename}]({file_path})\n\n*Error: Could not process image with OCR.*"

def convert_office_to_markdown(file_path: str, converter: MarkItDown, doc_type: str) -> str:
    """Convert office documents to markdown."""
    try:
        logger.info(f"Converting {doc_type} to Markdown")
        result = converter.convert(file_path)
        content = result.text if hasattr(result, 'text') else str(result)

        if not content or len(content.strip()) == 0:
            return f"# {doc_type}\n\n*Note: No content could be extracted from this file.*"

        return content

    except Exception as e:
        logger.error(f"Error converting {doc_type}: {str(e)}")
        return f"# {doc_type}\n\n*Error: Could not convert this file.*\n\nError details: {str(e)}"

def format_ocr_text_as_markdown(ocr_text: str) -> str:
    """
    Format OCR text as markdown with proper structure.

    Args:
        ocr_text (str): Raw OCR text

    Returns:
        str: Formatted markdown text
    """
    if not ocr_text:
        return ""

    # Split text into paragraphs
    paragraphs = ocr_text.split("\n\n")

    # Format each paragraph
    formatted_paragraphs = []
    for paragraph in paragraphs:
        # Skip empty paragraphs
        if not paragraph.strip():
            continue

        # Check if paragraph looks like a heading (short, ends with colon, all caps, etc.)
        lines = paragraph.split("\n")
        formatted_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Heuristic for headings: short lines that are all caps or end with colon
            if (len(line) < 100 and (line.isupper() or line.endswith(":"))) or i == 0:
                # Make it a heading based on length and position
                if len(line) < 20:
                    formatted_lines.append(f"## {line}")
                elif len(line) < 50:
                    formatted_lines.append(f"### {line}")
                else:
                    formatted_lines.append(f"#### {line}")
            else:
                # Regular paragraph text
                formatted_lines.append(line)

        # Join the formatted lines
        formatted_paragraph = "\n".join(formatted_lines)
        formatted_paragraphs.append(formatted_paragraph)

    # Join all formatted paragraphs with double newlines
    return "\n\n".join(formatted_paragraphs)

def convert_text_to_markdown(file_path: str, file_extension: str) -> str:
    """Convert text files to markdown."""
    try:
        logger.info(f"Reading text file: {file_extension}")

        # Try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
        content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                logger.info(f"Successfully read file with {encoding} encoding")
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            raise Exception("Could not decode file with any supported encoding")

        # Add appropriate header based on file type
        if file_extension == '.md':
            return content  # Markdown files don't need modification
        elif file_extension == '.rst':
            return f"# ReStructuredText Document\n\n```rst\n{content}\n```"
        else:
            return f"# Text Document\n\n```\n{content}\n```"

    except Exception as e:
        logger.error(f"Error reading text file: {str(e)}")
        return f"# Text Document\n\n*Error: Could not read this text file.*\n\nError details: {str(e)}"

def convert_csv_to_markdown(file_path: str) -> str:
    """Convert CSV to markdown table."""
    try:
        logger.info("Converting CSV to Markdown table")

        # Try different encodings and delimiters
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
        delimiters = [',', ';', '\t', '|']

        for encoding in encodings:
            for delimiter in delimiters:
                try:
                    with open(file_path, 'r', encoding=encoding, newline='') as f:
                        # Read a sample to detect delimiter
                        sample = f.read(1024)
                        f.seek(0)

                        # Use csv.Sniffer to detect delimiter
                        sniffer = csv.Sniffer()
                        detected_delimiter = sniffer.sniff(sample).delimiter

                        # Read the CSV
                        reader = csv.reader(f, delimiter=detected_delimiter)
                        rows = list(reader)

                        if len(rows) == 0:
                            continue

                        # Convert to markdown table
                        markdown_lines = ["# CSV Data\n"]

                        # Header row
                        if len(rows) > 0:
                            header = "| " + " | ".join(str(cell) for cell in rows[0]) + " |"
                            separator = "|" + "|".join(" --- " for _ in rows[0]) + "|"
                            markdown_lines.append(header)
                            markdown_lines.append(separator)

                            # Data rows (limit to first 100 rows for performance)
                            for row in rows[1:101]:
                                if len(row) == len(rows[0]):  # Ensure consistent column count
                                    data_row = "| " + " | ".join(str(cell) for cell in row) + " |"
                                    markdown_lines.append(data_row)

                            if len(rows) > 101:
                                markdown_lines.append(f"\n*Note: Showing first 100 rows of {len(rows)-1} total data rows.*")

                        return "\n".join(markdown_lines)

                except (UnicodeDecodeError, csv.Error):
                    continue

        raise Exception("Could not parse CSV file with any supported encoding or delimiter")

    except Exception as e:
        logger.error(f"Error converting CSV: {str(e)}")
        return f"# CSV Document\n\n*Error: Could not convert this CSV file.*\n\nError details: {str(e)}"

def convert_json_to_markdown(file_path: str) -> str:
    """Convert JSON to markdown."""
    try:
        logger.info("Converting JSON to Markdown")

        # Try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    data = json.load(f)

                # Format JSON as markdown
                markdown_content = ["# JSON Document\n"]
                markdown_content.append("```json")
                markdown_content.append(json.dumps(data, indent=2, ensure_ascii=False))
                markdown_content.append("```")

                return "\n".join(markdown_content)

            except (UnicodeDecodeError, json.JSONDecodeError):
                continue

        raise Exception("Could not parse JSON file")

    except Exception as e:
        logger.error(f"Error converting JSON: {str(e)}")
        return f"# JSON Document\n\n*Error: Could not convert this JSON file.*\n\nError details: {str(e)}"

def convert_xml_to_markdown(file_path: str) -> str:
    """Convert XML to markdown."""
    try:
        logger.info("Converting XML to Markdown")

        # Read and parse XML
        tree = ET.parse(file_path)
        root = tree.getroot()

        # Convert to formatted string
        ET.indent(tree, space="  ", level=0)
        xml_string = ET.tostring(root, encoding='unicode')

        # Format as markdown
        markdown_content = ["# XML Document\n"]
        markdown_content.append(f"**Root Element:** `{root.tag}`\n")
        markdown_content.append("```xml")
        markdown_content.append(xml_string)
        markdown_content.append("```")

        return "\n".join(markdown_content)

    except Exception as e:
        logger.error(f"Error converting XML: {str(e)}")
        return f"# XML Document\n\n*Error: Could not convert this XML file.*\n\nError details: {str(e)}"

def convert_generic_to_markdown(file_path: str, converter: MarkItDown, file_extension: str) -> str:
    """Convert unsupported file types using generic conversion."""
    try:
        logger.warning(f"Unsupported file type: {file_extension}, attempting generic conversion")
        result = converter.convert(file_path)
        content = result.text if hasattr(result, 'text') else str(result)

        if not content or len(content.strip()) == 0:
            return f"# Unknown File Type ({file_extension})\n\n*Note: This file type is not supported and no content could be extracted.*"

        return f"# Document ({file_extension})\n\n{content}"

    except Exception as e:
        logger.error(f"Generic conversion failed: {str(e)}")
        return f"# Unknown File Type ({file_extension})\n\n*Error: Could not convert this file.*\n\nError details: {str(e)}"

def clean_markdown(markdown_content: str) -> str:
    """
    Clean up markdown content for better processing.

    Args:
        markdown_content (str): Raw markdown content

    Returns:
        str: Cleaned markdown content
    """
    # Kiểm tra nếu markdown_content không phải chuỗi
    if not isinstance(markdown_content, str):
        logger.warning(f"markdown_content is not a string, type: {type(markdown_content)}. Converting to string.")
        markdown_content = str(markdown_content)

    if not markdown_content:
        return ""

    # Normalize line endings
    markdown_content = markdown_content.replace('\r\n', '\n').replace('\r', '\n')

    # Remove consecutive blank lines (limit to max 2)
    lines = markdown_content.split('\n')
    cleaned_lines = []
    blank_count = 0

    for line in lines:
        if line.strip() == '':
            blank_count += 1
            if blank_count <= 2:  # Keep max 2 consecutive blank lines
                cleaned_lines.append(line)
        else:
            blank_count = 0
            cleaned_lines.append(line)

    # Join lines back together
    cleaned_content = '\n'.join(cleaned_lines)

    # Remove trailing whitespace on each line
    cleaned_content = '\n'.join(line.rstrip() for line in cleaned_content.split('\n'))

    return cleaned_content
