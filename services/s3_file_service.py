"""Service for S3 file operations and file management."""

import os
import logging
import uuid
import time
import requests
import nanoid
from typing import Dict, Any, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from models.file import File
from core.s3_client import download_file_from_s3, generate_temp_file_path
from core.config import get_settings
from utils.string_utils import sanitize_filename

# Configure logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()


def generate_file_id() -> str:
    """
    Generate a unique file ID with 'file-' prefix.

    Returns:
        str: A unique file ID in the format 'file-XXXX'
    """
    # Generate a 16-character nanoid
    unique_id = nanoid.generate(size=16)
    return f"file-{unique_id}"


def validate_s3_url(url: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Validate an S3 URL by checking if it exists and is accessible.

    Args:
        url (str): The S3 URL to validate

    Returns:
        Tuple[bool, Optional[Dict[str, Any]]]: (is_valid, metadata)
    """
    try:
        # Check if URL is properly formatted
        if not url.startswith(('http://', 'https://')):
            logger.warning(f"Invalid URL format: {url}")
            return False, None

        # Make a HEAD request to check if the file exists
        response = requests.head(url, timeout=10)

        # Check if the request was successful
        if response.status_code != 200:
            logger.warning(f"URL not accessible: {url}, status code: {response.status_code}")
            return False, None

        # Extract metadata from headers
        metadata = {
            'content_type': response.headers.get('Content-Type', 'application/octet-stream'),
            'content_length': int(response.headers.get('Content-Length', 0)),
            'last_modified': response.headers.get('Last-Modified')
        }

        return True, metadata

    except requests.RequestException as e:
        logger.error(f"Error validating URL {url}: {str(e)}")
        return False, None
    except Exception as e:
        logger.error(f"Unexpected error validating URL {url}: {str(e)}")
        return False, None


def create_file_record(
    db: Session,
    url: str,
    filename: Optional[str] = None,
    filesize: Optional[int] = None
) -> File:
    """
    Create a new file record in the database.

    Args:
        db (Session): Database session
        url (str): S3 URL of the file
        filename (Optional[str]): Name of the file (extracted from URL if not provided)
        filesize (Optional[int]): Size of the file in bytes

    Returns:
        File: The created file record

    Raises:
        Exception: If file creation fails
    """
    try:
        # Extract filename from URL if not provided
        if not filename:
            filename = os.path.basename(url)

        # Validate URL and get metadata
        is_valid, metadata = validate_s3_url(url)
        if not is_valid:
            raise ValueError(f"Invalid or inaccessible URL: {url}")

        # Use provided filesize or get from metadata
        if filesize is None and metadata:
            filesize = metadata.get('content_length', 0)

        # Generate a unique file ID
        file_id = generate_file_id()

        # Create file record
        file = File(
            id=file_id,
            filename=filename,
            filepath=url,
            filesize=filesize,
            uploaded_at=int(time.time() * 1000)
        )

        # Add to database
        db.add(file)
        db.commit()
        db.refresh(file)

        logger.info(f"Created file record: {file_id}, {filename}")
        return file

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating file record: {str(e)}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating file record: {str(e)}")
        raise


def get_file_by_id(db: Session, file_id: str) -> Optional[File]:
    """
    Get a file by ID.

    Args:
        db (Session): Database session
        file_id (str): ID of the file

    Returns:
        Optional[File]: The file or None if not found
    """
    try:
        return db.query(File).filter(File.id == file_id).first()
    except Exception as e:
        logger.error(f"Error getting file by ID {file_id}: {str(e)}")
        raise


def download_file_to_temp(
    file_id: str,
    url: str,
    file_extension: Optional[str] = None
) -> str:
    """
    Download a file from S3 to a temporary location.

    Args:
        file_id (str): ID of the file
        url (str): S3 URL of the file
        file_extension (Optional[str]): File extension

    Returns:
        str: Path to the downloaded temporary file

    Raises:
        Exception: If download fails
    """
    try:
        # Generate a unique ID for the temp file
        temp_uuid = str(uuid.uuid4())

        # If no extension provided, try to extract from URL
        if not file_extension and '.' in os.path.basename(url):
            file_extension = os.path.basename(url).split('.')[-1]

        # Generate temporary file path
        temp_file_path = generate_temp_file_path(file_id, file_extension)

        # Download file
        downloaded_path = download_file_from_s3(url, temp_file_path)

        logger.info(f"Downloaded file to temporary path: {downloaded_path}")
        return downloaded_path

    except Exception as e:
        logger.error(f"Error downloading file to temp: {str(e)}")
        raise


def create_file_record_fast(
    db: Session,
    url: str,
    filename: Optional[str] = None,
    filesize: Optional[int] = None
) -> File:
    """
    Create a new file record in the database quickly without URL validation.
    This is used for immediate response to users.

    Args:
        db (Session): Database session
        url (str): S3 URL of the file
        filename (Optional[str]): Name of the file (extracted from URL if not provided)
        filesize (Optional[int]): Size of the file in bytes (will be 0 if not provided)

    Returns:
        File: The created file record

    Raises:
        Exception: If file creation fails
    """
    try:
        # Extract filename from URL if not provided
        if not filename:
            filename = os.path.basename(url)

        # Use provided filesize or default to 0 (will be updated later)
        if filesize is None:
            filesize = 0

        # Generate a unique file ID
        file_id = generate_file_id()

        # Create file record
        file = File(
            id=file_id,
            filename=filename,
            filepath=url,
            filesize=filesize,
            uploaded_at=int(time.time() * 1000)
        )

        # Add to database
        db.add(file)
        db.commit()
        db.refresh(file)

        logger.info(f"Created file record fast: {file_id}, {filename}")
        return file

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating file record fast: {str(e)}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating file record fast: {str(e)}")
        raise
