"""Service for image processing and OCR operations."""

import os
import logging
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from pdf2image import convert_from_path
import pytesseract

# Configure logger
logger = logging.getLogger(__name__)

def is_pdf_valid(pdf_path: str) -> bool:
    """
    Check if PDF file is valid and readable.

    Args:
        pdf_path (str): Path to the PDF file

    Returns:
        bool: True if PDF is valid, False otherwise
    """
    try:
        # Try to get page count using pdf2image
        from pdf2image import pdfinfo_from_path
        info = pdfinfo_from_path(pdf_path)
        if info.get("Pages", 0) > 0:
            return True
        return False
    except Exception as e:
        logger.warning(f"PDF validation failed: {str(e)}")
        return False

def convert_pdf_to_images(
    pdf_path: str,
    output_dir: Optional[str] = None,
    dpi: int = 300,
    output_format: str = 'PNG'
) -> List[str]:
    """
    Convert a PDF file to high-resolution images.

    Args:
        pdf_path (str): Path to the PDF file
        output_dir (Optional[str]): Directory to save the images (creates temp dir if None)
        dpi (int): Resolution of the output images (DPI)
        output_format (str): Format of the output images (PNG recommended)

    Returns:
        List[str]: List of paths to the generated images

    Raises:
        Exception: If conversion fails
    """
    try:
        logger.info(f"Converting PDF to images: {pdf_path} (DPI: {dpi})")

        # Validate PDF first
        if not is_pdf_valid(pdf_path):
            logger.warning("PDF validation failed, attempting conversion anyway")

        # Create temporary directory if output_dir is not provided
        temp_dir = None
        if output_dir is None:
            temp_dir = tempfile.TemporaryDirectory()
            output_dir = temp_dir.name

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Try different conversion methods
        images = None

        # Method 1: Standard conversion
        try:
            images = convert_from_path(
                pdf_path,
                dpi=dpi,
                output_folder=output_dir,
                fmt=output_format.lower(),
                paths_only=True
            )
        except Exception as e1:
            logger.warning(f"Standard PDF conversion failed: {str(e1)}")

            # Method 2: Lower DPI conversion
            try:
                logger.info("Trying with lower DPI (150)")
                images = convert_from_path(
                    pdf_path,
                    dpi=150,
                    output_folder=output_dir,
                    fmt=output_format.lower(),
                    paths_only=True
                )
            except Exception as e2:
                logger.warning(f"Lower DPI conversion failed: {str(e2)}")

                # Method 3: Basic conversion without output folder
                try:
                    logger.info("Trying basic conversion")
                    images = convert_from_path(pdf_path, dpi=150)
                    # Save images manually
                    saved_paths = []
                    for i, img in enumerate(images):
                        img_path = os.path.join(output_dir, f"page_{i+1}.{output_format.lower()}")
                        img.save(img_path, output_format.upper())
                        saved_paths.append(img_path)
                    images = saved_paths
                except Exception as e3:
                    logger.error(f"All PDF conversion methods failed: {str(e3)}")
                    raise Exception(f"Could not convert PDF to images. Last error: {str(e3)}")

        if not images:
            raise Exception("No images were generated from PDF")

        # If images are PIL objects, convert to paths
        if images and hasattr(images[0], 'save'):
            saved_paths = []
            for i, img in enumerate(images):
                img_path = os.path.join(output_dir, f"page_{i+1}.{output_format.lower()}")
                img.save(img_path, output_format.upper())
                saved_paths.append(img_path)
            images = saved_paths

        # Rename files to follow the required naming convention if needed
        renamed_paths = []
        for i, img_path in enumerate(images):
            if not os.path.basename(img_path).startswith("page_"):
                # Get the directory and original filename
                dir_path = os.path.dirname(img_path)
                # Create new filename with the required format
                new_filename = f"page_{i+1}.{output_format.lower()}"
                new_path = os.path.join(dir_path, new_filename)

                # Rename the file
                os.rename(img_path, new_path)
                renamed_paths.append(new_path)
            else:
                renamed_paths.append(img_path)

        logger.info(f"Successfully converted PDF to {len(renamed_paths)} images")
        return renamed_paths

    except Exception as e:
        logger.error(f"Error converting PDF to images: {str(e)}")
        raise
    finally:
        # Clean up temporary directory if created
        if temp_dir:
            temp_dir.cleanup()

def process_image(
    image_path: str,
    output_path: Optional[str] = None,
    apply_grayscale: bool = True,
    apply_sharpen: bool = True,
    apply_threshold: bool = True,
    threshold_value: int = 150
) -> str:
    """
    Process an image for OCR by applying various enhancements.

    Args:
        image_path (str): Path to the input image
        output_path (Optional[str]): Path to save the processed image (overwrites input if None)
        apply_grayscale (bool): Whether to convert to grayscale
        apply_sharpen (bool): Whether to apply sharpening
        apply_threshold (bool): Whether to apply binary thresholding
        threshold_value (int): Threshold value for binary conversion (0-255)

    Returns:
        str: Path to the processed image

    Raises:
        Exception: If processing fails
    """
    try:
        logger.info(f"Processing image: {image_path}")

        # Open the image
        img = Image.open(image_path)

        # Convert to grayscale if requested
        if apply_grayscale:
            img = img.convert("L")
            logger.info("Applied grayscale conversion")

        # Apply sharpening if requested
        if apply_sharpen:
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(2.0)  # Enhance sharpness by factor of 2
            logger.info("Applied sharpening")

        # Apply binary thresholding if requested
        if apply_threshold:
            # Convert to numpy array for thresholding
            img_array = np.array(img)
            # Apply threshold
            img_array = np.where(img_array < threshold_value, 0, 255).astype(np.uint8)
            # Convert back to PIL Image
            img = Image.fromarray(img_array)
            logger.info(f"Applied binary thresholding with value {threshold_value}")

        # Determine output path
        if output_path is None:
            output_path = image_path

        # Save the processed image
        img.save(output_path)
        logger.info(f"Saved processed image to {output_path}")

        return output_path

    except Exception as e:
        logger.error(f"Error processing image: {str(e)}")
        raise

def perform_ocr(
    image_path: str,
    lang: str = "eng+vie",
    config: str = "--psm 6 --oem 3"
) -> str:
    """
    Perform OCR on an image to extract text.

    Args:
        image_path (str): Path to the image
        lang (str): Language(s) for OCR
        config (str): Tesseract configuration

    Returns:
        str: Extracted text

    Raises:
        Exception: If OCR fails
    """
    try:
        logger.info(f"Performing OCR on image: {image_path} (lang: {lang}, config: {config})")

        # Open the image
        img = Image.open(image_path)

        # Perform OCR
        text = pytesseract.image_to_string(img, lang=lang, config=config)

        logger.info(f"Successfully extracted {len(text)} characters from image")
        return text

    except Exception as e:
        logger.error(f"Error performing OCR: {str(e)}")
        raise

def clean_ocr_text(text: str) -> str:
    """
    Clean and normalize OCR text.

    Args:
        text (str): Raw OCR text

    Returns:
        str: Cleaned and normalized text
    """
    if not text:
        return ""

    # Replace special characters
    text = text.replace("•", "-").replace("▪", "-").replace("‣", "-")

    # Remove invalid Unicode characters
    text = ''.join(char for char in text if ord(char) < 65536)

    # Replace problematic character combinations
    text = text.replace("ﬁ", "fi").replace("ﬂ", "fl")

    # Normalize whitespace
    lines = text.split("\n")
    cleaned_lines = []
    blank_count = 0

    for line in lines:
        if line.strip() == '':
            blank_count += 1
            if blank_count <= 1:  # Keep only one blank line
                cleaned_lines.append(line)
        else:
            blank_count = 0
            cleaned_lines.append(line)

    # Join lines back together
    cleaned_text = '\n'.join(cleaned_lines)

    return cleaned_text

def process_pdf_with_ocr(
    pdf_path: str,
    output_dir: Optional[str] = None,
    dpi: int = 300,
    lang: str = "eng+vie",
    apply_grayscale: bool = True,
    apply_sharpen: bool = True,
    apply_threshold: bool = True,
    threshold_value: int = 150
) -> str:
    """
    Process a PDF file with OCR to extract text.

    Args:
        pdf_path (str): Path to the PDF file
        output_dir (Optional[str]): Directory to save intermediate files
        dpi (int): Resolution for PDF to image conversion
        lang (str): Language(s) for OCR
        apply_grayscale (bool): Whether to convert to grayscale
        apply_sharpen (bool): Whether to apply sharpening
        apply_threshold (bool): Whether to apply binary thresholding
        threshold_value (int): Threshold value for binary conversion

    Returns:
        str: Extracted and cleaned text from all pages

    Raises:
        Exception: If processing fails
    """
    try:
        logger.info(f"Processing PDF with OCR: {pdf_path}")

        # Create temporary directory if output_dir is not provided
        temp_dir = None
        if output_dir is None:
            temp_dir = tempfile.TemporaryDirectory()
            output_dir = temp_dir.name

        # Convert PDF to images
        image_paths = convert_pdf_to_images(pdf_path, output_dir, dpi)

        # Process each image and perform OCR
        all_text = []
        for img_path in image_paths:
            # Process the image
            processed_path = process_image(
                img_path,
                apply_grayscale=apply_grayscale,
                apply_sharpen=apply_sharpen,
                apply_threshold=apply_threshold,
                threshold_value=threshold_value
            )

            # Perform OCR
            text = perform_ocr(processed_path, lang=lang)

            # Clean the text
            cleaned_text = clean_ocr_text(text)

            # Add to the list of texts
            all_text.append(cleaned_text)

        # Combine all text with page separators
        combined_text = "\n\n".join(all_text)

        logger.info(f"Successfully extracted and cleaned text from {len(image_paths)} pages")
        return combined_text

    except Exception as e:
        logger.error(f"Error processing PDF with OCR: {str(e)}")
        raise
    finally:
        # Clean up temporary directory if created
        if temp_dir:
            temp_dir.cleanup()

def process_image_with_ocr(
    image_path: str,
    output_dir: Optional[str] = None,
    lang: str = "eng+vie",
    apply_grayscale: bool = True,
    apply_sharpen: bool = True,
    apply_threshold: bool = True,
    threshold_value: int = 150
) -> str:
    """
    Process an image file with OCR to extract text.

    Args:
        image_path (str): Path to the image file
        output_dir (Optional[str]): Directory to save processed image
        lang (str): Language(s) for OCR
        apply_grayscale (bool): Whether to convert to grayscale
        apply_sharpen (bool): Whether to apply sharpening
        apply_threshold (bool): Whether to apply binary thresholding
        threshold_value (int): Threshold value for binary conversion

    Returns:
        str: Extracted and cleaned text

    Raises:
        Exception: If processing fails
    """
    try:
        logger.info(f"Processing image with OCR: {image_path}")

        # Create temporary directory if output_dir is not provided
        temp_dir = None
        if output_dir is None:
            temp_dir = tempfile.TemporaryDirectory()
            output_dir = temp_dir.name

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Generate output path for processed image
        filename = os.path.basename(image_path)
        processed_path = os.path.join(output_dir, f"processed_{filename}")

        # Process the image
        processed_path = process_image(
            image_path,
            output_path=processed_path,
            apply_grayscale=apply_grayscale,
            apply_sharpen=apply_sharpen,
            apply_threshold=apply_threshold,
            threshold_value=threshold_value
        )

        # Perform OCR
        text = perform_ocr(processed_path, lang=lang)

        # Clean the text
        cleaned_text = clean_ocr_text(text)

        logger.info(f"Successfully extracted and cleaned text from image")
        return cleaned_text

    except Exception as e:
        logger.error(f"Error processing image with OCR: {str(e)}")
        raise
    finally:
        # Clean up temporary directory if created
        if temp_dir:
            temp_dir.cleanup()