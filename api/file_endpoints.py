"""API endpoints for file operations."""
import logging
import time
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from core.database import get_db
from core.config import get_settings
from schemas.document_chunks import DocumentChunkRead
from services import s3_file_service, document_chunks_service, queue_service

# Configure logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create router
router = APIRouter(prefix="/files", tags=["files"])


class S3UrlRequest(BaseModel):
    """Request model for processing an S3 URL."""
    url: str = Field(..., description="URL S3 chứa tài liệu cần xử lý")
    chunk_size: Optional[int] = Field(None, description="Kích thước tùy chỉnh của mỗi đoạn tính bằng số ký tự (mặc định: 2000)", ge=100, le=10000)
    chunk_overlap: Optional[int] = Field(None, description="<PERSON><PERSON> chồng lấp tùy chỉnh giữa các đoạn tính bằng số ký tự (mặc định: 100)", ge=0, le=1000)
    vector_store_id: Optional[str] = Field(None, description="ID của kho vector để lưu trữ các đoạn (nếu không cung cấp, các đoạn sẽ không được liên kết với kho vector nào)")

    class Config:
        json_schema_extra = {
            "examples": [{
                "url": "https://cdn.redai.vn/knowledge_files/4/34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf",
                "chunk_size": 2000,
                "chunk_overlap": 100,
                "vector_store_id": "vs_681c63f7b8d48191a3394cb3b025b0e7"
            }]
        }


class ProcessResponse(BaseModel):
    """Response model for file processing."""
    message: str = Field(..., description="Thông báo kết quả")
    file_id: str = Field(..., description="ID của tập tin đã xử lý")
    filename: str = Field(..., description="Tên tập tin")
    status: str = Field(..., description="Trạng thái xử lý")
    chunk_size: int = Field(..., description="Kích thước đoạn đã sử dụng")
    chunk_overlap: int = Field(..., description="Độ chồng lấp đã sử dụng")
    vector_store_id: Optional[str] = Field(None, description="ID của kho vector được sử dụng (nếu có)")
    task_id: Optional[str] = Field(None, description="ID của task xử lý trong queue")

    class Config:
        json_schema_extra = {
            "examples": [{
                "message": "Đã tạo file record và bắt đầu xử lý trong nền",
                "file_id": "file-KBYXBHVZX8MMk4BP",
                "filename": "example.pdf",
                "status": "processing",
                "chunk_size": 2000,
                "chunk_overlap": 100,
                "vector_store_id": "vs_681c63f7b8d48191a3394cb3b025b0e7",
                "task_id": "task-12345"
            }]
        }


class ProcessProgressResponse(BaseModel):
    """Response model for file processing progress."""
    file_id: str = Field(..., description="ID của tập tin đang xử lý")
    status: str = Field(..., description="Trạng thái xử lý")
    progress: int = Field(..., description="Tiến độ xử lý (0-100)")
    message: str = Field(..., description="Thông báo tiến độ")
    details: Optional[str] = Field(None, description="Chi tiết bổ sung")
    timestamp: Optional[int] = Field(None, description="Thời gian cập nhật cuối cùng")

    class Config:
        json_schema_extra = {
            "examples": [{
                "file_id": "file-KBYXBHVZX8MMk4BP",
                "status": "processing",
                "progress": 75,
                "message": "Đang tạo embeddings",
                "details": "Đã xử lý 15/20 đoạn",
                "timestamp": 1640995200000
            }]
        }


@router.post(
    "/process-url",
    response_model=ProcessResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Xử lý tập tin từ URL S3",
    description="""
    Xử lý tập tin từ URL S3 để tạo các đoạn nội dung và vector embedding.

    **API trả về file ID ngay lập tức** sau khi tạo file record trong database,
    sau đó xử lý file trong nền (background) sử dụng Huey queue.

    Quy trình:
    1. Tạo file record và trả về file ID ngay lập tức
    2. Queue task xử lý file trong background
    3. Tải tập tin từ URL S3 (trong nền)
    4. Chuyển đổi tập tin sang định dạng Markdown (trong nền)
    5. Chia nhỏ nội dung thành các đoạn (trong nền)
    6. Tạo vector embedding cho mỗi đoạn (trong nền)
    7. Lưu trữ các đoạn và embedding vào database (trong nền)

    Tham số:
    - url: URL S3 chứa tài liệu cần xử lý
    - chunk_size: Kích thước tùy chỉnh của mỗi đoạn (mặc định: 2000 ký tự)
    - chunk_overlap: Độ chồng lấp giữa các đoạn (mặc định: 100 ký tự)
    - vector_store_id: ID của kho vector để lưu trữ các đoạn (tùy chọn)

    **Lưu ý:** Quá trình xử lý diễn ra trong nền sử dụng Huey queue.
    Sử dụng endpoint `/api/files/{file_id}/progress` để kiểm tra tiến độ xử lý.
    """
)
async def process_url(
    request: S3UrlRequest,
    db: Session = Depends(get_db)
):
    """
    Process a file from an S3 URL using Huey queue.

    Args:
        request (S3UrlRequest): Request with S3 URL and optional chunk parameters
        db (Session): Database session

    Returns:
        ProcessResponse: Response with file ID and processing status
    """
    try:
        # Basic URL format validation only (no network request)
        if not request.url.startswith(('http://', 'https://')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="URL không hợp lệ - phải bắt đầu bằng http:// hoặc https://"
            )

        # Create file record immediately without validation
        file = s3_file_service.create_file_record_fast(
            db=db,
            url=request.url
        )

        # Use default or custom chunk parameters
        chunk_size = request.chunk_size or settings.CHUNK_SIZE
        chunk_overlap = request.chunk_overlap or settings.CHUNK_OVERLAP

        # Kiểm tra vector_store_id nếu được cung cấp
        vector_store_id = request.vector_store_id
        if not vector_store_id:
            # Nếu không có vector_store_id, sử dụng giá trị mặc định
            vector_store_id = "vs_default"
            request.vector_store_id = vector_store_id
        else:
            # Kiểm tra định dạng vector_store_id
            if not vector_store_id.startswith("vs_"):
                logger.warning(f"Vector store ID không đúng định dạng: {vector_store_id}, sử dụng giá trị mặc định")
                vector_store_id = "vs_default"
                request.vector_store_id = vector_store_id

        # Queue file processing task using Huey
        task_id = queue_service.queue_file_processing(
            file_id=file.id,
            url=file.filepath,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            vector_store_id=request.vector_store_id
        )

        return ProcessResponse(
            message="Đã tạo file record và bắt đầu xử lý trong nền với Huey queue",
            file_id=file.id,
            filename=file.filename,
            status="processing",
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            vector_store_id=request.vector_store_id,
            task_id=task_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi xử lý tập tin: {str(e)}"
        )


@router.get(
    "/{file_id}/progress",
    response_model=ProcessProgressResponse,
    summary="Kiểm tra tiến độ xử lý tập tin",
    description="Lấy thông tin về tiến độ xử lý của một tập tin đang được xử lý trong nền bằng Huey queue."
)
async def get_processing_progress(
    file_id: str = Path(..., description="ID của tập tin cần kiểm tra tiến độ"),
    db: Session = Depends(get_db)
):
    """
    Get the progress of file processing from Huey queue.

    Args:
        file_id (str): ID of the file
        db (Session): Database session

    Returns:
        ProcessProgressResponse: Processing progress
    """
    try:
        # Check if file exists
        file = s3_file_service.get_file_by_id(db, file_id)
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Không tìm thấy tập tin"
            )

        # Get progress from queue service
        progress_info = queue_service.get_task_progress(file_id)

        if progress_info["status"] == "not_found":
            # If no progress info, check if file has chunks (completed)
            chunks_count = document_chunks_service.count_chunks_by_file_id(db, file_id)
            if chunks_count > 0:
                # File has been processed
                return ProcessProgressResponse(
                    file_id=file_id,
                    status="completed",
                    progress=100,
                    message=f"Đã hoàn thành xử lý với {chunks_count} đoạn",
                    details=None,
                    timestamp=int(time.time() * 1000)
                )
            else:
                # File not processed yet
                return ProcessProgressResponse(
                    file_id=file_id,
                    status="pending",
                    progress=0,
                    message="Chưa bắt đầu xử lý",
                    details=None,
                    timestamp=int(time.time() * 1000)
                )

        return ProcessProgressResponse(**progress_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file progress: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi lấy thông tin tiến độ: {str(e)}"
        )


@router.get(
    "/{file_id}/chunks",
    response_model=List[DocumentChunkRead],
    summary="Lấy các đoạn nội dung của tập tin",
    description="""
    Lấy danh sách các đoạn nội dung đã được xử lý của một tập tin.

    Các đoạn nội dung được trả về theo thứ tự của chỉ số đoạn (chunk_index).
    Có thể giới hạn số lượng đoạn trả về bằng tham số `limit` và bỏ qua một số đoạn đầu tiên bằng tham số `skip`.
    """
)
async def get_file_chunks(
    file_id: str = Path(..., description="ID của tập tin cần lấy các đoạn nội dung"),
    skip: int = Query(0, description="Số lượng đoạn bỏ qua (dùng cho phân trang)"),
    limit: int = Query(100, description="Số lượng đoạn tối đa trả về (dùng cho phân trang)"),
    db: Session = Depends(get_db)
):
    """
    Get chunks for a file.

    Args:
        file_id (str): ID of the file
        skip (int): Number of chunks to skip (for pagination)
        limit (int): Maximum number of chunks to return (for pagination)
        db (Session): Database session

    Returns:
        List[DocumentChunkRead]: List of document chunks
    """
    try:
        # Check if file exists
        file = s3_file_service.get_file_by_id(db, file_id)
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Không tìm thấy tập tin với ID: {file_id}"
            )

        # Get chunks for the file
        chunks = document_chunks_service.get_chunks_for_file(
            db=db,
            file_id=file_id,
            skip=skip,
            limit=limit
        )

        # Convert to response model
        return [
            DocumentChunkRead(
                id=chunk["id"],
                file_id=chunk["file_id"],
                chunk_index=chunk["chunk_index"],
                content=chunk["content"],
                chunk_metadata=chunk["metadata"],
                embedding_source=chunk["embedding_source"],
                embedding_dimensions=chunk["embedding_dimensions"],
                created_at=chunk["created_at"],
                updated_at=chunk["updated_at"]
            )
            for chunk in chunks
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chunks for file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi lấy các đoạn nội dung: {str(e)}"
        )
