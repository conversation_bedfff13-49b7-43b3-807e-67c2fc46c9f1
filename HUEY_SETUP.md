# Hướng dẫn sử dụng Huey Queue System

## Tổng quan

Hệ thống đã được cập nhật để sử dụng **Huey** với SQLite backend thay vì FastAPI BackgroundTasks. Điều này mang lại những lợi ích sau:

### Ưu điểm của Huey:
- ✅ **Không cần Redis**: Sử dụng SQLite làm backend, không cần cài đặt service bên ngoài
- ✅ **Persistent**: Tasks không bị mất khi restart server
- ✅ **Retry mechanism**: Tự động retry khi task fail
- ✅ **Scalable**: <PERSON><PERSON> thể chạy nhiều worker
- ✅ **Monitoring**: <PERSON><PERSON> thể theo dõi trạng thái tasks
- ✅ **Immediate response**: API trả về file ID ngay lập tức

## Cách hoạt động

### Trước (FastAPI BackgroundTasks):
1. Client gọi API `/api/files/process-url`
2. API tạo file record
3. API queue task vào BackgroundTasks
4. API trả về response
5. BackgroundTasks xử lý file trong cùng process

**Vấn đề**: Tasks mất khi restart, không có retry, khó scale

### Sau (Huey Queue):
1. Client gọi API `/api/files/process-url`
2. API tạo file record
3. API queue task vào Huey SQLite database
4. API trả về response ngay lập tức với file ID
5. Huey worker (process riêng) xử lý file từ queue
6. Progress được lưu trong SQLite database

**Lợi ích**: Persistent, có retry, scalable, immediate response

## Cách chạy

### 1. Chạy API Server
```bash
uvicorn main:app --host 0.0.0.0 --port 3000 --reload
```

### 2. Chạy Huey Worker (Terminal mới)
```bash
python run_worker.py
```

### 3. Hoặc sử dụng Docker Compose
```bash
docker-compose -f docker-compose.worker.yml up
```

## Cấu trúc files mới

### `services/queue_service.py`
- Cấu hình Huey với SQLite backend
- Task function `process_file_task()` 
- Progress tracking functions
- Queue management functions

### `run_worker.py`
- Script để chạy Huey worker
- Xử lý background tasks

### `api/file_endpoints.py` (đã cập nhật)
- Sử dụng `queue_service.queue_file_processing()` thay vì BackgroundTasks
- Progress endpoint sử dụng `queue_service.get_task_progress()`

### `queue_data/` (thư mục mới)
- `huey_queue.db`: SQLite database cho Huey queue
- `progress.db`: SQLite database cho progress tracking

## API Endpoints

### 1. Process File (Không đổi)
```
POST /api/files/process-url
```

**Request:**
```json
{
  "url": "https://example.com/file.pdf",
  "chunk_size": 2000,
  "chunk_overlap": 100,
  "vector_store_id": "vs_example"
}
```

**Response (Ngay lập tức):**
```json
{
  "message": "Đã tạo file record và bắt đầu xử lý trong nền với Huey queue",
  "file_id": "file-XXXXXXXXX",
  "filename": "file.pdf",
  "status": "processing",
  "chunk_size": 2000,
  "chunk_overlap": 100,
  "vector_store_id": "vs_example",
  "task_id": "task-12345"
}
```

### 2. Check Progress (Cải thiện)
```
GET /api/files/{file_id}/progress
```

**Response:**
```json
{
  "file_id": "file-XXXXXXXXX",
  "status": "processing",
  "progress": 75,
  "message": "Đang tạo embeddings cho 10 chunks",
  "details": "Đã xử lý 7/10 chunks",
  "timestamp": 1640995200000
}
```

### 3. Get Chunks (Không đổi)
```
GET /api/files/{file_id}/chunks
```

## Monitoring

### 1. Kiểm tra Queue Status
```bash
# Xem số lượng tasks trong queue
python -c "
from services.queue_service import huey
print(f'Pending tasks: {len(huey)}')
"
```

### 2. Xem Worker Logs
```bash
tail -f worker.log
```

### 3. Xem Progress Database
```bash
sqlite3 queue_data/progress.db "SELECT * FROM task_progress;"
```

## Troubleshooting

### 1. Worker không chạy
- Kiểm tra `python run_worker.py` có lỗi không
- Kiểm tra file `worker.log`
- Đảm bảo thư mục `queue_data/` có quyền write

### 2. Tasks không được xử lý
- Kiểm tra worker có đang chạy không
- Kiểm tra database connection
- Xem logs trong `worker.log`

### 3. Progress không cập nhật
- Kiểm tra file `queue_data/progress.db` có tồn tại không
- Kiểm tra quyền write cho thư mục `queue_data/`

## Migration từ hệ thống cũ

Nếu bạn đang sử dụng hệ thống cũ:

1. **Backup data**: Backup database và files
2. **Update code**: Pull code mới
3. **Install Huey**: `pip install huey~=2.5.3`
4. **Create directories**: `mkdir queue_data`
5. **Start worker**: `python run_worker.py`
6. **Test**: Gọi API và kiểm tra progress

## Performance

### Với 1 worker:
- Xử lý 1 file tại một thời điểm
- Phù hợp cho development và small scale

### Với nhiều workers:
```bash
# Chạy 3 workers
python run_worker.py &
python run_worker.py &
python run_worker.py &
```

### Docker scaling:
```yaml
worker:
  image: sonpl2603/redai-rag-engine:latest
  deploy:
    replicas: 3
```

## Kết luận

Hệ thống Huey queue mang lại:
- **Reliability**: Tasks không bị mất
- **Scalability**: Có thể chạy nhiều workers
- **Simplicity**: Không cần Redis hay RabbitMQ
- **Immediate response**: Client nhận file ID ngay lập tức
- **Better UX**: Client có thể track progress realtime

Hệ thống này phù hợp cho production và có thể scale theo nhu cầu.
