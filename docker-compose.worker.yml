version: '3.8'

services:
  app:
    image: sonpl2603/redai-rag-engine:latest
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./temp-s3:/app/temp-s3
      - ./queue_data:/app/queue_data
      - ./app.log:/app/app.log
    restart: unless-stopped
    depends_on:
      - worker

  worker:
    image: sonpl2603/redai-rag-engine:latest
    env_file:
      - .env
    volumes:
      - ./temp-s3:/app/temp-s3
      - ./queue_data:/app/queue_data
      - ./worker.log:/app/worker.log
    command: ["python", "run_worker.py"]
    restart: unless-stopped
