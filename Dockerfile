FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=UTF-8

# Install system dependencies including FFmpeg, Tesseract OCR, and build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    postgresql-client \
    build-essential \
    libpq-dev \
    gcc \
    python3-dev \
    tesseract-ocr \
    libtesseract-dev \
    tesseract-ocr-vie \
    tesseract-ocr-eng \
    poppler-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Upgrade pip and install Python dependencies
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir markitdown[all] && \
    pip install --no-cache-dir psycopg2-binary && \
    pip install --no-cache-dir google-generativeai

# Copy project files
COPY . .

# Create temp directory for file processing and queue data
RUN mkdir -p temp-s3 && chmod 777 temp-s3 && \
    mkdir -p queue_data && chmod 777 queue_data

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "3000"]
